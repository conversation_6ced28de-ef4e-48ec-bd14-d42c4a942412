// تطبيق الطقس - ملف JavaScript الرئيسي

// إعدادات عامة
const WeatherApp = {
    // إعدادات API
    apiEndpoint: '/api/weather/',
    
    // عناصر DOM
    elements: {
        citySearch: null,
        searchBtn: null,
        weatherPreviews: null,
        forecastCards: null
    },
    
    // تهيئة التطبيق
    init() {
        this.bindElements();
        this.bindEvents();
        this.loadWeatherPreviews();
        this.initAnimations();
        console.log('تم تحميل تطبيق الطقس بنجاح');
    },
    
    // ربط العناصر
    bindElements() {
        this.elements.citySearch = document.getElementById('citySearch');
        this.elements.searchBtn = document.getElementById('searchBtn');
        this.elements.weatherPreviews = document.querySelectorAll('.weather-preview');
        this.elements.forecastCards = document.querySelectorAll('.forecast-card');
    },
    
    // ربط الأحداث
    bindEvents() {
        // البحث عند النقر على الزر
        if (this.elements.searchBtn) {
            this.elements.searchBtn.addEventListener('click', () => {
                this.handleSearch();
            });
        }
        
        // البحث عند الضغط على Enter
        if (this.elements.citySearch) {
            this.elements.citySearch.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.handleSearch();
                }
            });
        }
        
        // تأثيرات hover للبطاقات
        this.elements.forecastCards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                this.animateCard(card, 'enter');
            });
            
            card.addEventListener('mouseleave', () => {
                this.animateCard(card, 'leave');
            });
        });
        
        // تحديث الوقت
        this.updateTime();
        setInterval(() => this.updateTime(), 60000);
    },
    
    // تحميل معاينات الطقس
    loadWeatherPreviews() {
        this.elements.weatherPreviews.forEach(preview => {
            const city = preview.dataset.city;
            this.fetchWeatherData(city, preview);
        });
    },
    
    // جلب بيانات الطقس
    async fetchWeatherData(city, targetElement) {
        try {
            // عرض مؤشر التحميل
            this.showLoading(targetElement);
            
            const response = await fetch(`${this.apiEndpoint}${encodeURIComponent(city)}`);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.current_weather) {
                this.displayWeatherPreview(data.current_weather, targetElement);
            } else {
                this.showError(targetElement, 'لا توجد بيانات متاحة');
            }
            
        } catch (error) {
            console.error('خطأ في جلب بيانات الطقس:', error);
            this.showError(targetElement, 'خطأ في التحميل');
        }
    },
    
    // عرض معاينة الطقس
    displayWeatherPreview(weather, targetElement) {
        const temp = Math.round(weather.main.temp);
        const description = weather.weather[0].description;
        const icon = weather.weather[0].icon;
        
        targetElement.innerHTML = `
            <div class="weather-info fade-in">
                <div class="d-flex align-items-center justify-content-center">
                    <img src="https://openweathermap.org/img/wn/${icon}.png" 
                         alt="${description}" 
                         class="weather-icon-small me-2" 
                         style="width: 32px;">
                    <div class="text-center">
                        <div class="temperature h5 mb-0 text-primary fw-bold">
                            ${temp}°C
                        </div>
                        <div class="description small text-muted">
                            ${description}
                        </div>
                    </div>
                </div>
            </div>
        `;
    },
    
    // عرض مؤشر التحميل
    showLoading(targetElement) {
        targetElement.innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="min-height: 60px;">
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
            </div>
        `;
    },
    
    // عرض رسالة خطأ
    showError(targetElement, message) {
        targetElement.innerHTML = `
            <div class="text-center text-muted small">
                <i class="bi bi-exclamation-triangle mb-1"></i>
                <div>${message}</div>
            </div>
        `;
    },
    
    // معالجة البحث
    handleSearch() {
        const searchTerm = this.elements.citySearch?.value.trim();
        
        if (!searchTerm) {
            this.showAlert('يرجى إدخال اسم المدينة', 'warning');
            return;
        }
        
        // البحث في المدن المتاحة
        const availableCities = this.getAvailableCities();
        const foundCity = availableCities.find(city => 
            city.includes(searchTerm) || searchTerm.includes(city)
        );
        
        if (foundCity) {
            // الانتقال إلى صفحة المدينة
            window.location.href = `/weather/${encodeURIComponent(foundCity)}`;
        } else {
            this.showAlert('المدينة غير موجودة في قائمة المدن المتاحة', 'info');
        }
    },
    
    // الحصول على قائمة المدن المتاحة
    getAvailableCities() {
        const cityCards = document.querySelectorAll('.city-card');
        return Array.from(cityCards).map(card => {
            return card.querySelector('.card-title').textContent.trim();
        });
    },
    
    // عرض تنبيه
    showAlert(message, type = 'info') {
        // إنشاء عنصر التنبيه
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        alertDiv.innerHTML = `
            <i class="bi bi-info-circle-fill me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // إضافة التنبيه إلى الصفحة
        document.body.appendChild(alertDiv);
        
        // إزالة التنبيه تلقائياً بعد 5 ثوان
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    },
    
    // تحريك البطاقات
    animateCard(card, action) {
        if (action === 'enter') {
            card.style.transform = 'translateY(-5px) scale(1.02)';
            card.style.boxShadow = '0 10px 25px rgba(0,0,0,0.15)';
        } else {
            card.style.transform = 'translateY(0) scale(1)';
            card.style.boxShadow = '';
        }
    },
    
    // تهيئة الرسوم المتحركة
    initAnimations() {
        // تأثير fade-in للعناصر
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in');
                }
            });
        }, observerOptions);
        
        // مراقبة البطاقات
        document.querySelectorAll('.card').forEach(card => {
            observer.observe(card);
        });
    },
    
    // تحديث الوقت
    updateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
        });
        
        const timeElements = document.querySelectorAll('#last-update');
        timeElements.forEach(element => {
            element.textContent = timeString;
        });
    },
    
    // تحويل timestamp إلى وقت
    timestampToTime(timestamp) {
        const date = new Date(timestamp * 1000);
        return date.toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
        });
    },
    
    // تحديث بيانات الطقس
    async refreshWeatherData() {
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> جاري التحديث...';
        }
        
        // إعادة تحميل معاينات الطقس
        this.loadWeatherPreviews();
        
        setTimeout(() => {
            if (refreshBtn) {
                refreshBtn.disabled = false;
                refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise"></i> تحديث';
            }
        }, 2000);
    }
};

// إضافة CSS للرسوم المتحركة
const animationCSS = `
    .fade-in {
        animation: fadeIn 0.6s ease-in-out;
    }
    
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .spin {
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
    
    .weather-icon-small {
        transition: transform 0.3s ease;
    }
    
    .weather-icon-small:hover {
        transform: scale(1.1);
    }
`;

// إضافة CSS إلى الصفحة
const styleSheet = document.createElement('style');
styleSheet.textContent = animationCSS;
document.head.appendChild(styleSheet);

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    WeatherApp.init();
});

// تصدير الكائن للاستخدام العام
window.WeatherApp = WeatherApp;
