import requests
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional

class WeatherService:
    """خدمة للتعامل مع API الطقس"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.openweathermap.org/data/2.5"
        
    def get_current_weather(self, lat: float, lon: float, lang: str = 'ar') -> Optional[Dict]:
        """الحصول على حالة الطقس الحالية"""
        try:
            url = f"{self.base_url}/weather"
            params = {
                'lat': lat,
                'lon': lon,
                'appid': self.api_key,
                'units': 'metric',
                'lang': lang
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            # تنسيق البيانات
            return {
                'temperature': round(data['main']['temp']),
                'feels_like': round(data['main']['feels_like']),
                'humidity': data['main']['humidity'],
                'pressure': data['main']['pressure'],
                'visibility': data.get('visibility', 0) / 1000,  # تحويل إلى كيلومتر
                'wind_speed': data['wind']['speed'],
                'wind_direction': data['wind'].get('deg', 0),
                'description': data['weather'][0]['description'],
                'icon': data['weather'][0]['icon'],
                'main': data['weather'][0]['main'],
                'sunrise': datetime.fromtimestamp(data['sys']['sunrise']).strftime('%H:%M'),
                'sunset': datetime.fromtimestamp(data['sys']['sunset']).strftime('%H:%M'),
                'country': data['sys']['country'],
                'city_name': data['name']
            }
            
        except requests.exceptions.RequestException as e:
            print(f"خطأ في طلب API: {e}")
            return None
        except KeyError as e:
            print(f"خطأ في بنية البيانات: {e}")
            return None
        except Exception as e:
            print(f"خطأ غير متوقع: {e}")
            return None
    
    def get_forecast(self, lat: float, lon: float, lang: str = 'ar') -> Optional[List[Dict]]:
        """الحصول على تنبؤات الطقس لمدة 5 أيام"""
        try:
            url = f"{self.base_url}/forecast"
            params = {
                'lat': lat,
                'lon': lon,
                'appid': self.api_key,
                'units': 'metric',
                'lang': lang
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            return self._process_forecast_data(data)
            
        except requests.exceptions.RequestException as e:
            print(f"خطأ في طلب API للتنبؤات: {e}")
            return None
        except Exception as e:
            print(f"خطأ غير متوقع في التنبؤات: {e}")
            return None
    
    def _process_forecast_data(self, forecast_data: Dict) -> List[Dict]:
        """معالجة بيانات التنبؤات وتجميعها حسب اليوم"""
        if not forecast_data or 'list' not in forecast_data:
            return []
        
        daily_forecasts = {}
        
        for item in forecast_data['list']:
            # تحويل التوقيت إلى تاريخ
            date = datetime.fromtimestamp(item['dt']).date()
            
            if date not in daily_forecasts:
                daily_forecasts[date] = {
                    'date': date,
                    'temps': [],
                    'descriptions': [],
                    'icons': [],
                    'humidity': [],
                    'wind_speed': [],
                    'pressure': [],
                    'main_weather': []
                }
            
            # جمع البيانات لكل يوم
            daily_forecasts[date]['temps'].append(item['main']['temp'])
            daily_forecasts[date]['descriptions'].append(item['weather'][0]['description'])
            daily_forecasts[date]['icons'].append(item['weather'][0]['icon'])
            daily_forecasts[date]['humidity'].append(item['main']['humidity'])
            daily_forecasts[date]['wind_speed'].append(item['wind']['speed'])
            daily_forecasts[date]['pressure'].append(item['main']['pressure'])
            daily_forecasts[date]['main_weather'].append(item['weather'][0]['main'])
        
        # تجميع البيانات اليومية
        processed_forecasts = []
        day_names_ar = {
            'Monday': 'الإثنين',
            'Tuesday': 'الثلاثاء', 
            'Wednesday': 'الأربعاء',
            'Thursday': 'الخميس',
            'Friday': 'الجمعة',
            'Saturday': 'السبت',
            'Sunday': 'الأحد'
        }
        
        for date, data in sorted(daily_forecasts.items()):
            # تحديد الوصف والأيقونة الأكثر تكراراً
            most_common_desc = max(set(data['descriptions']), key=data['descriptions'].count)
            most_common_icon = max(set(data['icons']), key=data['icons'].count)
            most_common_main = max(set(data['main_weather']), key=data['main_weather'].count)
            
            day_name_en = date.strftime('%A')
            day_name_ar = day_names_ar.get(day_name_en, day_name_en)
            
            processed_forecasts.append({
                'date': date.strftime('%Y-%m-%d'),
                'day_name': day_name_ar,
                'temp_max': round(max(data['temps'])),
                'temp_min': round(min(data['temps'])),
                'temp_avg': round(sum(data['temps']) / len(data['temps'])),
                'description': most_common_desc,
                'icon': most_common_icon,
                'main': most_common_main,
                'humidity': round(sum(data['humidity']) / len(data['humidity'])),
                'wind_speed': round(sum(data['wind_speed']) / len(data['wind_speed']), 1),
                'pressure': round(sum(data['pressure']) / len(data['pressure']))
            })
        
        return processed_forecasts[:7]  # إرجاع 7 أيام فقط
    
    def get_weather_by_city_name(self, city_name: str, lang: str = 'ar') -> Optional[Dict]:
        """البحث عن الطقس باستخدام اسم المدينة"""
        try:
            url = f"{self.base_url}/weather"
            params = {
                'q': city_name,
                'appid': self.api_key,
                'units': 'metric',
                'lang': lang
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f"خطأ في البحث عن المدينة: {e}")
            return None
        except Exception as e:
            print(f"خطأ غير متوقع في البحث: {e}")
            return None
