#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تثبيت تطبيق الطقس
يقوم بتثبيت جميع المتطلبات وإعداد التطبيق
"""

import subprocess
import sys
import os
from pathlib import Path

def install_requirements():
    """تثبيت المتطلبات من ملف requirements.txt"""
    print("🔄 جاري تثبيت المتطلبات...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في تثبيت المتطلبات: {e}")
        return False

def setup_env_file():
    """إعداد ملف البيئة"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        print("🔄 جاري إنشاء ملف .env...")
        try:
            # نسخ ملف المثال
            with open(env_example, 'r', encoding='utf-8') as f:
                content = f.read()
            
            with open(env_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ تم إنشاء ملف .env")
            print("⚠️  يرجى إضافة مفتاح OpenWeatherMap API في ملف .env")
            return True
        except Exception as e:
            print(f"❌ خطأ في إنشاء ملف .env: {e}")
            return False
    elif env_file.exists():
        print("✅ ملف .env موجود بالفعل")
        return True
    else:
        print("❌ ملف .env.example غير موجود")
        return False

def check_api_key():
    """فحص وجود مفتاح API"""
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        api_key = os.getenv('OPENWEATHER_API_KEY')
        if api_key and api_key != 'your_api_key_here':
            print("✅ مفتاح API موجود")
            return True
        else:
            print("⚠️  مفتاح API غير موجود أو غير صحيح")
            print("📝 يرجى الحصول على مفتاح API من: https://openweathermap.org/api")
            print("📝 وإضافته في ملف .env")
            return False
    except ImportError:
        print("❌ لم يتم تثبيت python-dotenv بعد")
        return False
    except Exception as e:
        print(f"❌ خطأ في فحص مفتاح API: {e}")
        return False

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = ['templates', 'static', 'static/css', 'static/js', 'static/images']
    
    for directory in directories:
        dir_path = Path(directory)
        if not dir_path.exists():
            try:
                dir_path.mkdir(parents=True, exist_ok=True)
                print(f"✅ تم إنشاء مجلد: {directory}")
            except Exception as e:
                print(f"❌ خطأ في إنشاء مجلد {directory}: {e}")
                return False
        else:
            print(f"✅ مجلد {directory} موجود بالفعل")
    
    return True

def main():
    """الدالة الرئيسية"""
    print("🌤️  مرحباً بك في تطبيق الطقس!")
    print("=" * 50)
    
    # إنشاء المجلدات
    if not create_directories():
        print("❌ فشل في إنشاء المجلدات")
        return False
    
    # تثبيت المتطلبات
    if not install_requirements():
        print("❌ فشل في تثبيت المتطلبات")
        return False
    
    # إعداد ملف البيئة
    if not setup_env_file():
        print("❌ فشل في إعداد ملف البيئة")
        return False
    
    # فحص مفتاح API
    api_key_ok = check_api_key()
    
    print("\n" + "=" * 50)
    print("🎉 تم إعداد التطبيق بنجاح!")
    
    if not api_key_ok:
        print("\n⚠️  تذكير مهم:")
        print("1. احصل على مفتاح API مجاني من: https://openweathermap.org/api")
        print("2. أضف المفتاح في ملف .env")
        print("3. شغل التطبيق بالأمر: python app.py")
    else:
        print("\n🚀 يمكنك الآن تشغيل التطبيق بالأمر:")
        print("python app.py")
    
    print("\n📖 للمزيد من المعلومات، راجع ملف README.md")
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ تم إلغاء التثبيت")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
