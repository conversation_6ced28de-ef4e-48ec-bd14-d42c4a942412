from flask import Flask, render_template, request, jsonify
import requests
import os
from datetime import datetime, timedelta
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

app = Flask(__name__)

# إضافة فلاتر مخصصة لـ Jinja2
@app.template_filter('timestamp_to_time')
def timestamp_to_time(timestamp):
    """تحويل timestamp إلى وقت مقروء"""
    try:
        dt = datetime.fromtimestamp(timestamp)
        return dt.strftime('%H:%M')
    except:
        return "غير متوفر"

# إعدادات API
OPENWEATHER_API_KEY = os.getenv('OPENWEATHER_API_KEY')
OPENWEATHER_BASE_URL = "https://api.openweathermap.org/data/2.5"

# المدن الرئيسية في الشرق الأوسط مع إحداثياتها
MIDDLE_EAST_CITIES = {
    # دول الخليج العربي
    'الرياض': {'lat': 24.7136, 'lon': 46.6753, 'country': 'SA', 'country_name': 'السعودية'},
    'جدة': {'lat': 21.4858, 'lon': 39.1925, 'country': 'SA', 'country_name': 'السعودية'},
    'مكة المكرمة': {'lat': 21.3891, 'lon': 39.8579, 'country': 'SA', 'country_name': 'السعودية'},
    'المدينة المنورة': {'lat': 24.5247, 'lon': 39.5692, 'country': 'SA', 'country_name': 'السعودية'},
    'الدمام': {'lat': 26.4207, 'lon': 50.0888, 'country': 'SA', 'country_name': 'السعودية'},

    'دبي': {'lat': 25.2048, 'lon': 55.2708, 'country': 'AE', 'country_name': 'الإمارات'},
    'أبوظبي': {'lat': 24.2992, 'lon': 54.6972, 'country': 'AE', 'country_name': 'الإمارات'},
    'الشارقة': {'lat': 25.3463, 'lon': 55.4209, 'country': 'AE', 'country_name': 'الإمارات'},

    'الدوحة': {'lat': 25.2854, 'lon': 51.5310, 'country': 'QA', 'country_name': 'قطر'},

    'الكويت': {'lat': 29.3759, 'lon': 47.9774, 'country': 'KW', 'country_name': 'الكويت'},

    'المنامة': {'lat': 26.0667, 'lon': 50.5577, 'country': 'BH', 'country_name': 'البحرين'},

    'مسقط': {'lat': 23.5859, 'lon': 58.4059, 'country': 'OM', 'country_name': 'عمان'},
    'صلالة': {'lat': 17.0151, 'lon': 54.0924, 'country': 'OM', 'country_name': 'عمان'},

    # بلاد الشام
    'بيروت': {'lat': 33.8938, 'lon': 35.5018, 'country': 'LB', 'country_name': 'لبنان'},
    'طرابلس': {'lat': 34.4361, 'lon': 35.8497, 'country': 'LB', 'country_name': 'لبنان'},

    'دمشق': {'lat': 33.5138, 'lon': 36.2765, 'country': 'SY', 'country_name': 'سوريا'},
    'حلب': {'lat': 36.2021, 'lon': 37.1343, 'country': 'SY', 'country_name': 'سوريا'},

    'عمان': {'lat': 31.9454, 'lon': 35.9284, 'country': 'JO', 'country_name': 'الأردن'},
    'إربد': {'lat': 32.5556, 'lon': 35.8500, 'country': 'JO', 'country_name': 'الأردن'},

    'القدس': {'lat': 31.7683, 'lon': 35.2137, 'country': 'PS', 'country_name': 'فلسطين'},
    'غزة': {'lat': 31.3547, 'lon': 34.3088, 'country': 'PS', 'country_name': 'فلسطين'},
    'رام الله': {'lat': 31.9038, 'lon': 35.2034, 'country': 'PS', 'country_name': 'فلسطين'},

    'تل أبيب': {'lat': 32.0853, 'lon': 34.7818, 'country': 'IL', 'country_name': 'إسرائيل'},

    # العراق
    'بغداد': {'lat': 33.3152, 'lon': 44.3661, 'country': 'IQ', 'country_name': 'العراق'},
    'البصرة': {'lat': 30.5852, 'lon': 47.7911, 'country': 'IQ', 'country_name': 'العراق'},
    'أربيل': {'lat': 36.1911, 'lon': 44.0093, 'country': 'IQ', 'country_name': 'العراق'},
    'الموصل': {'lat': 36.3350, 'lon': 43.1189, 'country': 'IQ', 'country_name': 'العراق'},

    # مصر
    'القاهرة': {'lat': 30.0444, 'lon': 31.2357, 'country': 'EG', 'country_name': 'مصر'},
    'الإسكندرية': {'lat': 31.2001, 'lon': 29.9187, 'country': 'EG', 'country_name': 'مصر'},
    'الجيزة': {'lat': 30.0131, 'lon': 31.2089, 'country': 'EG', 'country_name': 'مصر'},
    'شرم الشيخ': {'lat': 27.9158, 'lon': 34.3300, 'country': 'EG', 'country_name': 'مصر'},

    # إيران
    'طهران': {'lat': 35.6892, 'lon': 51.3890, 'country': 'IR', 'country_name': 'إيران'},
    'أصفهان': {'lat': 32.6546, 'lon': 51.6680, 'country': 'IR', 'country_name': 'إيران'},
    'مشهد': {'lat': 36.2605, 'lon': 59.6168, 'country': 'IR', 'country_name': 'إيران'},

    # تركيا
    'أنقرة': {'lat': 39.9334, 'lon': 32.8597, 'country': 'TR', 'country_name': 'تركيا'},
    'إسطنبول': {'lat': 41.0082, 'lon': 28.9784, 'country': 'TR', 'country_name': 'تركيا'},
    'أنطاليا': {'lat': 36.8969, 'lon': 30.7133, 'country': 'TR', 'country_name': 'تركيا'},
    'إزمير': {'lat': 38.4192, 'lon': 27.1287, 'country': 'TR', 'country_name': 'تركيا'},

    # اليمن
    'صنعاء': {'lat': 15.3694, 'lon': 44.1910, 'country': 'YE', 'country_name': 'اليمن'},
    'عدن': {'lat': 12.7797, 'lon': 45.0365, 'country': 'YE', 'country_name': 'اليمن'},

    # المغرب العربي
    'الرباط': {'lat': 34.0209, 'lon': -6.8416, 'country': 'MA', 'country_name': 'المغرب'},
    'الدار البيضاء': {'lat': 33.5731, 'lon': -7.5898, 'country': 'MA', 'country_name': 'المغرب'},
    'مراكش': {'lat': 31.6295, 'lon': -7.9811, 'country': 'MA', 'country_name': 'المغرب'},

    'الجزائر': {'lat': 36.7538, 'lon': 3.0588, 'country': 'DZ', 'country_name': 'الجزائر'},
    'وهران': {'lat': 35.6911, 'lon': -0.6417, 'country': 'DZ', 'country_name': 'الجزائر'},

    'تونس': {'lat': 36.8065, 'lon': 10.1815, 'country': 'TN', 'country_name': 'تونس'},
    'صفاقس': {'lat': 34.7406, 'lon': 10.7603, 'country': 'TN', 'country_name': 'تونس'},

    'طرابلس الغرب': {'lat': 32.8872, 'lon': 13.1913, 'country': 'LY', 'country_name': 'ليبيا'},
    'بنغازي': {'lat': 32.1313, 'lon': 20.0685, 'country': 'LY', 'country_name': 'ليبيا'},

    # السودان
    'الخرطوم': {'lat': 15.5007, 'lon': 32.5599, 'country': 'SD', 'country_name': 'السودان'},
    'بورتسودان': {'lat': 19.6348, 'lon': 37.2163, 'country': 'SD', 'country_name': 'السودان'},
}

def get_weather_data(lat, lon):
    """الحصول على بيانات الطقس الحالية"""
    if not OPENWEATHER_API_KEY or OPENWEATHER_API_KEY == 'your_api_key_here':
        print("تحذير: مفتاح API غير صحيح")
        return None

    try:
        url = f"{OPENWEATHER_BASE_URL}/weather"
        params = {
            'lat': lat,
            'lon': lon,
            'appid': OPENWEATHER_API_KEY,
            'units': 'metric',
            'lang': 'ar'
        }
        print(f"طلب API للطقس: {url} مع المعاملات: {params}")
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()
        print(f"تم الحصول على بيانات الطقس بنجاح لـ {data.get('name', 'مدينة غير معروفة')}")
        return data
    except requests.exceptions.HTTPError as e:
        if response.status_code == 401:
            print(f"خطأ في مفتاح API: {e}")
        elif response.status_code == 404:
            print(f"المدينة غير موجودة: {e}")
        else:
            print(f"خطأ HTTP: {e}")
        return None
    except requests.exceptions.RequestException as e:
        print(f"خطأ في الشبكة: {e}")
        return None
    except Exception as e:
        print(f"خطأ غير متوقع في الحصول على بيانات الطقس: {e}")
        return None

def get_forecast_data(lat, lon):
    """الحصول على تنبؤات الطقس لمدة 7 أيام"""
    if not OPENWEATHER_API_KEY or OPENWEATHER_API_KEY == 'your_api_key_here':
        print("تحذير: مفتاح API غير صحيح للتنبؤات")
        return None

    try:
        url = f"{OPENWEATHER_BASE_URL}/forecast"
        params = {
            'lat': lat,
            'lon': lon,
            'appid': OPENWEATHER_API_KEY,
            'units': 'metric',
            'lang': 'ar'
        }
        print(f"طلب API للتنبؤات: {url}")
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()
        print(f"تم الحصول على تنبؤات الطقس بنجاح - {len(data.get('list', []))} نقطة بيانات")
        return data
    except requests.exceptions.HTTPError as e:
        if response.status_code == 401:
            print(f"خطأ في مفتاح API للتنبؤات: {e}")
        else:
            print(f"خطأ HTTP في التنبؤات: {e}")
        return None
    except requests.exceptions.RequestException as e:
        print(f"خطأ في الشبكة للتنبؤات: {e}")
        return None
    except Exception as e:
        print(f"خطأ غير متوقع في الحصول على تنبؤات الطقس: {e}")
        return None

def process_forecast_data(forecast_data):
    """معالجة بيانات التنبؤات لتجميعها حسب اليوم"""
    if not forecast_data:
        return []
    
    daily_forecasts = {}
    
    for item in forecast_data['list']:
        date = datetime.fromtimestamp(item['dt']).date()
        
        if date not in daily_forecasts:
            daily_forecasts[date] = {
                'date': date,
                'temps': [],
                'descriptions': [],
                'icons': [],
                'humidity': [],
                'wind_speed': []
            }
        
        daily_forecasts[date]['temps'].append(item['main']['temp'])
        daily_forecasts[date]['descriptions'].append(item['weather'][0]['description'])
        daily_forecasts[date]['icons'].append(item['weather'][0]['icon'])
        daily_forecasts[date]['humidity'].append(item['main']['humidity'])
        daily_forecasts[date]['wind_speed'].append(item['wind']['speed'])
    
    # تجميع البيانات اليومية
    processed_forecasts = []
    for date, data in sorted(daily_forecasts.items()):
        processed_forecasts.append({
            'date': date.strftime('%Y-%m-%d'),
            'day_name': date.strftime('%A'),
            'temp_max': round(max(data['temps'])),
            'temp_min': round(min(data['temps'])),
            'description': data['descriptions'][0],  # أخذ أول وصف
            'icon': data['icons'][0],  # أخذ أول أيقونة
            'humidity': round(sum(data['humidity']) / len(data['humidity'])),
            'wind_speed': round(sum(data['wind_speed']) / len(data['wind_speed']), 1)
        })
    
    return processed_forecasts[:7]  # إرجاع 7 أيام فقط

def get_demo_weather_data(city_name):
    """إرجاع بيانات تجريبية للطقس عند عدم توفر API"""
    import random
    from datetime import datetime

    # بيانات تجريبية متنوعة حسب المنطقة
    temp_ranges = {
        'الرياض': (25, 45), 'جدة': (28, 42), 'دبي': (30, 48),
        'الدوحة': (28, 46), 'الكويت': (25, 50), 'المنامة': (26, 44),
        'مسقط': (28, 45), 'بيروت': (18, 32), 'دمشق': (15, 35),
        'عمان': (12, 30), 'بغداد': (20, 48), 'القاهرة': (18, 38),
        'طهران': (10, 35), 'أنقرة': (5, 30), 'إسطنبول': (8, 28)
    }

    temp_range = temp_ranges.get(city_name, (20, 35))
    temp = random.randint(temp_range[0], temp_range[1])

    weather_conditions = [
        {'main': 'Clear', 'description': 'سماء صافية', 'icon': '01d'},
        {'main': 'Clouds', 'description': 'غيوم متفرقة', 'icon': '02d'},
        {'main': 'Rain', 'description': 'أمطار خفيفة', 'icon': '10d'},
        {'main': 'Dust', 'description': 'عاصفة ترابية', 'icon': '50d'}
    ]

    condition = random.choice(weather_conditions)

    return {
        'name': city_name,
        'main': {
            'temp': temp,
            'feels_like': temp + random.randint(-3, 3),
            'humidity': random.randint(30, 80),
            'pressure': random.randint(1000, 1020)
        },
        'weather': [condition],
        'wind': {
            'speed': random.randint(5, 25)
        },
        'visibility': random.randint(5000, 10000),
        'sys': {
            'sunrise': 1609459200,  # 6:00 AM
            'sunset': 1609502400,   # 6:00 PM
            'country': 'DEMO'
        }
    }

def get_demo_forecast_data():
    """إرجاع بيانات تنبؤات تجريبية"""
    from datetime import datetime, timedelta
    import random

    forecasts = []
    days = ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت']

    for i in range(7):
        date = datetime.now() + timedelta(days=i)
        day_name = days[date.weekday()]

        temp_max = random.randint(25, 45)
        temp_min = temp_max - random.randint(5, 15)

        conditions = [
            {'description': 'سماء صافية', 'icon': '01d'},
            {'description': 'غيوم متفرقة', 'icon': '02d'},
            {'description': 'غائم جزئياً', 'icon': '03d'},
            {'description': 'أمطار خفيفة', 'icon': '10d'}
        ]

        condition = random.choice(conditions)

        forecasts.append({
            'date': date.strftime('%Y-%m-%d'),
            'day_name': day_name,
            'temp_max': temp_max,
            'temp_min': temp_min,
            'description': condition['description'],
            'icon': condition['icon'],
            'humidity': random.randint(40, 80),
            'wind_speed': random.randint(5, 20)
        })

    return forecasts

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template('index.html', cities=MIDDLE_EAST_CITIES)

@app.route('/weather/<city>')
def weather(city):
    """صفحة عرض الطقس لمدينة معينة"""
    if city not in MIDDLE_EAST_CITIES:
        return "المدينة غير موجودة", 404

    city_data = MIDDLE_EAST_CITIES[city]

    # الحصول على بيانات الطقس الحالية
    current_weather = get_weather_data(city_data['lat'], city_data['lon'])

    # إذا فشل API، استخدم بيانات تجريبية
    if not current_weather:
        print(f"استخدام بيانات تجريبية لمدينة {city}")
        current_weather = get_demo_weather_data(city)

    # الحصول على تنبؤات الطقس
    forecast_data = get_forecast_data(city_data['lat'], city_data['lon'])
    forecast = process_forecast_data(forecast_data)

    # إذا فشلت التنبؤات، استخدم بيانات تجريبية
    if not forecast:
        print(f"استخدام تنبؤات تجريبية لمدينة {city}")
        forecast = get_demo_forecast_data()

    return render_template('weather.html',
                         city=city,
                         current_weather=current_weather,
                         forecast=forecast)

@app.route('/api/weather/<city>')
def api_weather(city):
    """API للحصول على بيانات الطقس"""
    if city not in MIDDLE_EAST_CITIES:
        return jsonify({'error': 'المدينة غير موجودة'}), 404

    city_data = MIDDLE_EAST_CITIES[city]

    # الحصول على بيانات الطقس الحالية
    current_weather = get_weather_data(city_data['lat'], city_data['lon'])

    # إذا فشل API، استخدم بيانات تجريبية
    if not current_weather:
        current_weather = get_demo_weather_data(city)

    # الحصول على تنبؤات الطقس
    forecast_data = get_forecast_data(city_data['lat'], city_data['lon'])
    forecast = process_forecast_data(forecast_data)

    # إذا فشلت التنبؤات، استخدم بيانات تجريبية
    if not forecast:
        forecast = get_demo_forecast_data()

    return jsonify({
        'city': city,
        'current_weather': current_weather,
        'forecast': forecast,
        'data_source': 'demo' if not OPENWEATHER_API_KEY or OPENWEATHER_API_KEY == 'your_api_key_here' else 'api'
    })

if __name__ == '__main__':
    if not OPENWEATHER_API_KEY:
        print("تحذير: لم يتم العثور على مفتاح API. يرجى إضافة OPENWEATHER_API_KEY في ملف .env")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
