{% extends "base.html" %}

{% block title %}الصفحة الرئيسية - تطبيق الطقس{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-section text-center py-5 mb-5">
    <div class="container">
        <h1 class="display-4 fw-bold text-primary mb-3">
            <i class="bi bi-cloud-sun-fill me-3"></i>
            مرحباً بك في تطبيق الطقس
        </h1>
        <p class="lead text-muted mb-4">
            تابع أحوال الطقس والتنبؤات لمدة أسبوع في المدن الرئيسية بالشرق الأوسط
        </p>
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="input-group input-group-lg">
                    <input type="text" class="form-control" placeholder="ابحث عن مدينة..." id="citySearch">
                    <button class="btn btn-primary" type="button" id="searchBtn">
                        <i class="bi bi-search"></i>
                        بحث
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Cities Grid -->
<div class="row">
    <div class="col-12">
        <h2 class="h3 mb-4 text-center">
            <i class="bi bi-geo-alt-fill me-2 text-primary"></i>
            المدن الرئيسية في الشرق الأوسط
        </h2>
    </div>
    
    {% for city, data in cities.items() %}
    <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
        <div class="card city-card h-100 shadow-sm">
            <div class="card-body text-center">
                <div class="city-icon mb-3">
                    <i class="bi bi-building display-4 text-primary"></i>
                </div>
                <h5 class="card-title fw-bold">{{ city }}</h5>
                <p class="card-text text-muted small">
                    <i class="bi bi-flag me-1"></i>
                    {{ data.country }}
                </p>
                
                <!-- Weather Preview (will be loaded via JS) -->
                <div class="weather-preview" data-city="{{ city }}" data-lat="{{ data.lat }}" data-lon="{{ data.lon }}">
                    <div class="d-flex justify-content-center">
                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <a href="{{ url_for('weather', city=city) }}" class="btn btn-primary btn-sm">
                        <i class="bi bi-eye me-1"></i>
                        عرض التفاصيل
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Features Section -->
<div class="row mt-5">
    <div class="col-12">
        <h2 class="h3 mb-4 text-center">
            <i class="bi bi-star-fill me-2 text-warning"></i>
            مميزات التطبيق
        </h2>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card feature-card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="feature-icon mb-3">
                    <i class="bi bi-thermometer-sun display-4 text-warning"></i>
                </div>
                <h5 class="card-title">حالة الطقس الحالية</h5>
                <p class="card-text text-muted">
                    احصل على معلومات دقيقة عن درجة الحرارة والرطوبة وسرعة الرياح
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card feature-card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="feature-icon mb-3">
                    <i class="bi bi-calendar-week display-4 text-info"></i>
                </div>
                <h5 class="card-title">تنبؤات لمدة أسبوع</h5>
                <p class="card-text text-muted">
                    خطط لأسبوعك مع تنبؤات دقيقة لمدة 7 أيام قادمة
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card feature-card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="feature-icon mb-3">
                    <i class="bi bi-globe-asia-australia display-4 text-success"></i>
                </div>
                <h5 class="card-title">تغطية شاملة</h5>
                <p class="card-text text-muted">
                    جميع المدن الرئيسية في منطقة الشرق الأوسط في مكان واحد
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// تحميل معاينة الطقس للمدن
document.addEventListener('DOMContentLoaded', function() {
    const weatherPreviews = document.querySelectorAll('.weather-preview');
    
    weatherPreviews.forEach(preview => {
        const city = preview.dataset.city;
        
        // استدعاء API للحصول على بيانات الطقس
        fetch(`/api/weather/${encodeURIComponent(city)}`)
            .then(response => response.json())
            .then(data => {
                if (data.current_weather) {
                    const weather = data.current_weather;
                    preview.innerHTML = `
                        <div class="weather-info">
                            <div class="temperature h4 mb-1 text-primary fw-bold">
                                ${Math.round(weather.main.temp)}°C
                            </div>
                            <div class="description small text-muted">
                                ${weather.weather[0].description}
                            </div>
                        </div>
                    `;
                } else {
                    preview.innerHTML = `
                        <div class="text-muted small">
                            <i class="bi bi-exclamation-triangle"></i>
                            غير متوفر
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('خطأ في تحميل بيانات الطقس:', error);
                preview.innerHTML = `
                    <div class="text-muted small">
                        <i class="bi bi-exclamation-triangle"></i>
                        خطأ في التحميل
                    </div>
                `;
            });
    });
});

// البحث عن المدن
document.getElementById('searchBtn').addEventListener('click', function() {
    const searchTerm = document.getElementById('citySearch').value.trim();
    if (searchTerm) {
        // يمكن إضافة وظيفة البحث هنا
        alert('وظيفة البحث قيد التطوير');
    }
});

// البحث عند الضغط على Enter
document.getElementById('citySearch').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        document.getElementById('searchBtn').click();
    }
});
</script>
{% endblock %}
