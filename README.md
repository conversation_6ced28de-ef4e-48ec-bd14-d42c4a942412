# 🌤️ تطبيق الطقس - Weather App

تطبيق ويب شامل لعرض حالة الطقس والتنبؤات لمدة أسبوع للمدن الرئيسية في الشرق الأوسط، مبني باستخدام Flask مع واجهة عربية جميلة ومتجاوبة.

## ✨ المميزات

- 🌡️ **عرض حالة الطقس الحالية** - درجة الحرارة، الرطوبة، سرعة الرياح، الضغط الجوي
- 📅 **تنبؤات الطقس لمدة 7 أيام** - تنبؤات دقيقة مع تفاصيل شاملة
- 🗺️ **تغطية شاملة للشرق الأوسط** - أكثر من 50 مدينة رئيسية
- 🇸🇦 **واجهة باللغة العربية** - تصميم يدعم الكتابة من اليمين لليسار
- 📱 **تصميم متجاوب** - يعمل بشكل مثالي على جميع الأجهزة
- 🎨 **واجهة جميلة وحديثة** - تصميم عصري مع تأثيرات تفاعلية
- ⚡ **سرعة في التحميل** - استجابة سريعة وتحديث فوري
- 🔍 **بحث ذكي** - إمكانية البحث عن المدن

## 🌍 المدن المدعومة

### دول الخليج العربي
- **السعودية**: الرياض، جدة، مكة المكرمة، المدينة المنورة، الدمام
- **الإمارات**: دبي، أبوظبي، الشارقة
- **قطر**: الدوحة
- **الكويت**: الكويت
- **البحرين**: المنامة
- **عمان**: مسقط، صلالة

### بلاد الشام
- **لبنان**: بيروت، طرابلس
- **سوريا**: دمشق، حلب
- **الأردن**: عمان، إربد
- **فلسطين**: القدس، غزة، رام الله
- **إسرائيل**: تل أبيب

### دول أخرى
- **العراق**: بغداد، البصرة، أربيل، الموصل
- **مصر**: القاهرة، الإسكندرية، الجيزة، شرم الشيخ
- **إيران**: طهران، أصفهان، مشهد
- **تركيا**: أنقرة، إسطنبول، أنطاليا، إزمير
- **اليمن**: صنعاء، عدن
- **المغرب العربي**: الرباط، الدار البيضاء، الجزائر، تونس، طرابلس الغرب
- **السودان**: الخرطوم، بورتسودان

## 🚀 التثبيت والتشغيل

### الطريقة السريعة (Windows)
1. قم بتشغيل ملف `start.bat`
2. سيتم تثبيت المتطلبات تلقائياً
3. أضف مفتاح API في ملف `.env`
4. التطبيق سيعمل على `http://localhost:5000`

### التثبيت اليدوي

#### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

#### 2. إعداد ملف البيئة
```bash
# انسخ ملف المثال
cp .env.example .env

# أو قم بإنشاء ملف .env وأضف:
OPENWEATHER_API_KEY=your_api_key_here
```

#### 3. تشغيل التطبيق
```bash
# الطريقة الأولى
python app.py

# أو الطريقة الثانية
python run.py
```

#### 4. فتح التطبيق
افتح المتصفح وانتقل إلى: `http://localhost:5000`

## 🔑 الحصول على مفتاح API

1. قم بزيارة [OpenWeatherMap](https://openweathermap.org/api)
2. أنشئ حساب مجاني
3. انتقل إلى قسم "API Keys"
4. انسخ مفتاح API
5. أضف المفتاح في ملف `.env`:
   ```
   OPENWEATHER_API_KEY=your_actual_api_key_here
   ```

> **ملاحظة**: الحساب المجاني يسمح بـ 1000 طلب في الساعة، وهو كافٍ للاستخدام الشخصي.

## 📁 هيكل المشروع

```
weather/
├── app.py                 # الملف الرئيسي للتطبيق
├── weather_service.py     # خدمة API للطقس
├── run.py                 # ملف تشغيل مبسط
├── install.py             # سكريبت التثبيت
├── start.bat              # ملف تشغيل Windows
├── requirements.txt       # المتطلبات
├── .env                   # متغيرات البيئة
├── .env.example          # مثال لملف البيئة
├── README.md             # هذا الملف
├── templates/            # قوالب HTML
│   ├── base.html         # القالب الأساسي
│   ├── index.html        # الصفحة الرئيسية
│   └── weather.html      # صفحة تفاصيل الطقس
└── static/               # الملفات الثابتة
    ├── css/
    │   └── style.css     # ملف التنسيق
    └── js/
        └── app.js        # ملف JavaScript
```

## 🛠️ التقنيات المستخدمة

- **Backend**: Flask (Python)
- **Frontend**: HTML5, CSS3, JavaScript
- **UI Framework**: Bootstrap 5
- **Icons**: Bootstrap Icons
- **Fonts**: Noto Sans Arabic
- **API**: OpenWeatherMap API
- **Styling**: CSS Grid, Flexbox, Gradients

## 📱 واجهات التطبيق

### الصفحة الرئيسية
- عرض جميع المدن المدعومة
- معاينة سريعة للطقس لكل مدينة
- شريط بحث للعثور على المدن
- تصميم بطاقات جميل ومتجاوب

### صفحة تفاصيل المدينة
- حالة الطقس الحالية مع جميع التفاصيل
- تنبؤات لمدة 7 أيام
- معلومات شروق وغروب الشمس
- بيانات الرطوبة والضغط وسرعة الرياح

### API Endpoints
- `GET /` - الصفحة الرئيسية
- `GET /weather/<city>` - صفحة تفاصيل المدينة
- `GET /api/weather/<city>` - بيانات JSON للطقس

## 🎨 المميزات التقنية

- **تصميم متجاوب**: يعمل على جميع أحجام الشاشات
- **دعم RTL**: واجهة مصممة للغة العربية
- **تأثيرات تفاعلية**: hover effects وانيميشن CSS
- **تحديث تلقائي**: تحديث الوقت والبيانات
- **معالجة الأخطاء**: رسائل خطأ واضحة ومفيدة
- **تحسين الأداء**: تحميل سريع وذاكرة تخزين مؤقت

## 🔧 التخصيص والتطوير

### إضافة مدن جديدة
قم بتعديل قاموس `MIDDLE_EAST_CITIES` في ملف `app.py`:

```python
MIDDLE_EAST_CITIES = {
    'اسم المدينة': {
        'lat': خط_العرض,
        'lon': خط_الطول,
        'country': 'رمز_الدولة',
        'country_name': 'اسم_الدولة'
    }
}
```

### تخصيص التصميم
- عدل ملف `static/css/style.css` لتغيير الألوان والخطوط
- استخدم متغيرات CSS للتخصيص السريع
- جميع الألوان تستخدم gradients قابلة للتخصيص

### إضافة ميزات جديدة
- أضف routes جديدة في `app.py`
- أنشئ قوالب HTML جديدة في مجلد `templates`
- استخدم `weather_service.py` للتفاعل مع APIs إضافية

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**1. خطأ "API key not found"**
```bash
# تأكد من وجود ملف .env مع مفتاح صحيح
OPENWEATHER_API_KEY=your_actual_api_key
```

**2. خطأ "Module not found"**
```bash
# تأكد من تثبيت جميع المتطلبات
pip install -r requirements.txt
```

**3. خطأ "Port already in use"**
```bash
# غير المنفذ في app.py أو أوقف العملية الأخرى
netstat -ano | findstr :5000
taskkill /PID <process_id> /F
```

**4. مشاكل في عرض الخطوط العربية**
- تأكد من أن المتصفح يدعم خطوط Google Fonts
- تحقق من اتصال الإنترنت لتحميل الخطوط

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتعليمي.

## 🤝 المساهمة

نرحب بالمساهمات! يمكنك:
- إضافة مدن جديدة
- تحسين التصميم
- إضافة ميزات جديدة
- إصلاح الأخطاء
- تحسين الترجمة

## 📞 الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يرجى:
1. التحقق من قسم استكشاف الأخطاء أعلاه
2. مراجعة ملفات السجل للأخطاء
3. التأكد من صحة مفتاح API

---

**تم تطوير هذا التطبيق بـ ❤️ لخدمة المجتمع العربي**
