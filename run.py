#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل تطبيق الطقس
"""

import os
import sys
from app import app

def main():
    """تشغيل التطبيق"""
    print("🌤️  بدء تشغيل تطبيق الطقس...")
    print("=" * 50)
    
    # التحقق من مفتاح API
    api_key = os.getenv('OPENWEATHER_API_KEY')
    if not api_key or api_key == 'your_api_key_here':
        print("⚠️  تحذير: لم يتم العثور على مفتاح API صحيح")
        print("📝 يرجى إضافة مفتاح OpenWeatherMap API في ملف .env")
        print("🔗 احصل على مفتاح مجاني من: https://openweathermap.org/api")
        print("=" * 50)
    
    print("🚀 التطبيق يعمل على: http://localhost:5000")
    print("🛑 لإيقاف التطبيق اضغط Ctrl+C")
    print("=" * 50)
    
    try:
        app.run(
            debug=True,
            host='0.0.0.0',
            port=5000,
            use_reloader=True
        )
    except KeyboardInterrupt:
        print("\n\n🛑 تم إيقاف التطبيق")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل التطبيق: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
