@echo off
chcp 65001 > nul
title تطبيق الطقس - Weather App

echo.
echo ========================================
echo       🌤️  تطبيق الطقس - Weather App
echo ========================================
echo.

echo 🔄 جاري التحقق من المتطلبات...

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت. يرجى تثبيت Python أولاً
    pause
    exit /b 1
)

REM التحقق من وجود pip
python -m pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير متوفر
    pause
    exit /b 1
)

echo ✅ Python متوفر

REM تثبيت المتطلبات إذا لم تكن مثبتة
echo 🔄 جاري تثبيت المتطلبات...
python -m pip install -r requirements.txt >nul 2>&1

REM التحقق من وجود ملف .env
if not exist ".env" (
    echo 🔄 جاري إنشاء ملف .env...
    copy ".env.example" ".env" >nul 2>&1
    echo ⚠️  يرجى إضافة مفتاح OpenWeatherMap API في ملف .env
    echo 🔗 احصل على مفتاح مجاني من: https://openweathermap.org/api
    echo.
)

echo ✅ جاهز للتشغيل
echo.
echo 🚀 بدء تشغيل التطبيق...
echo 🌐 سيتم فتح التطبيق على: http://localhost:5000
echo 🛑 لإيقاف التطبيق اضغط Ctrl+C
echo.
echo ========================================

REM تشغيل التطبيق
python app.py

echo.
echo 🛑 تم إيقاف التطبيق
pause
