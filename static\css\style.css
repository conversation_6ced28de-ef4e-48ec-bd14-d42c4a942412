/* تطبيق الطقس - ملف التنسيق الرئيسي */

/* الخطوط والإعدادات العامة */
body {
    font-family: 'Noto Sans Arabic', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    direction: rtl;
}

/* إعادة تعيين الاتجاه للعناصر التي تحتاج LTR */
.ltr {
    direction: ltr;
}

/* تنسيق الشريط العلوي */
.navbar {
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.5rem;
}

/* تنسيق المحتوى الرئيسي */
main {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    margin-top: 2rem;
    margin-bottom: 2rem;
    padding: 2rem;
}

/* قسم البطل */
.hero-section {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 15px;
    margin: -2rem -2rem 2rem -2rem;
    padding: 3rem 2rem !important;
}

.hero-section h1 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* بطاقات المدن */
.city-card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 15px;
    overflow: hidden;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
}

.city-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15) !important;
}

.city-card .city-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* بطاقة الطقس الحالي */
.current-weather-card {
    background: linear-gradient(135deg, #ffffff 0%, #f0f4ff 100%);
    border-radius: 20px;
    overflow: hidden;
}

.current-weather-card .temperature {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* بطاقات التفاصيل */
.detail-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.detail-card:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white;
    transform: scale(1.05);
}

.detail-card:hover .text-muted {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* بطاقات أوقات الشمس */
.sun-time-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 193, 7, 0.2);
}

.sun-time-card:hover {
    transform: scale(1.02);
}

/* بطاقات التنبؤات */
.forecast-card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 15px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
    overflow: hidden;
}

.forecast-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.forecast-card .temp-max {
    color: #dc3545;
}

.forecast-card .temp-min {
    color: #6c757d;
}

/* بطاقات المميزات */
.feature-card {
    transition: all 0.3s ease;
    border-radius: 15px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1) !important;
}

.feature-card .feature-icon {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 50%;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

/* أيقونات الطقس */
.weather-icon img {
    filter: drop-shadow(0 4px 8px rgba(0,0,0,0.1));
}

/* تنسيق الأزرار */
.btn {
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* تنسيق النماذج */
.form-control {
    border-radius: 10px;
    border: 2px solid rgba(102, 126, 234, 0.1);
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* تنسيق التذييل */
footer {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
}

/* تنسيق شريط التنقل */
.breadcrumb {
    background: rgba(102, 126, 234, 0.1);
    border-radius: 10px;
    padding: 0.75rem 1rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "←";
}

/* تأثيرات التحميل */
.spinner-border {
    animation: spinner-border 0.75s linear infinite;
}

/* تنسيق التنبيهات */
.alert {
    border-radius: 15px;
    border: none;
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%);
    color: #856404;
}

/* تنسيق متجاوب */
@media (max-width: 768px) {
    main {
        margin: 1rem;
        padding: 1rem;
        border-radius: 15px;
    }
    
    .hero-section {
        margin: -1rem -1rem 1rem -1rem;
        padding: 2rem 1rem !important;
    }
    
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .display-3 {
        font-size: 2.5rem;
    }
    
    .current-weather-card .row {
        text-align: center;
    }
    
    .current-weather-card .col-md-6:first-child {
        margin-bottom: 2rem;
    }
}

@media (max-width: 576px) {
    .hero-section h1 {
        font-size: 1.75rem;
    }
    
    .display-3 {
        font-size: 2rem;
    }
    
    .forecast-card {
        margin-bottom: 1rem;
    }
}

/* تأثيرات إضافية */
.weather-preview {
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.temperature-range {
    font-size: 1.1rem;
}

/* تحسينات الأداء */
.card, .btn, .form-control {
    will-change: transform;
}

/* تنسيق خاص للغة العربية */
.text-arabic {
    font-family: 'Noto Sans Arabic', sans-serif;
    font-weight: 400;
}

/* تنسيق الأرقام */
.number {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: ltr;
    display: inline-block;
}
