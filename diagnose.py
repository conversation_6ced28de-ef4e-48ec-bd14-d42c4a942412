#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة تشخيص تطبيق الطقس
تساعد في اكتشاف وحل مشاكل API والإعدادات
"""

import os
import requests
import sys
from dotenv import load_dotenv

def check_environment():
    """فحص متغيرات البيئة"""
    print("🔍 فحص متغيرات البيئة...")
    print("-" * 40)
    
    # تحميل متغيرات البيئة
    load_dotenv()
    
    # فحص ملف .env
    if os.path.exists('.env'):
        print("✅ ملف .env موجود")
        
        with open('.env', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'OPENWEATHER_API_KEY' in content:
                print("✅ متغير OPENWEATHER_API_KEY موجود في الملف")
            else:
                print("❌ متغير OPENWEATHER_API_KEY غير موجود في الملف")
    else:
        print("❌ ملف .env غير موجود")
        return False
    
    # فحص قيمة المتغير
    api_key = os.getenv('OPENWEATHER_API_KEY')
    if api_key:
        if api_key == 'your_api_key_here':
            print("⚠️  مفتاح API لم يتم تغييره من القيمة الافتراضية")
            return False
        elif len(api_key) == 32:
            print("✅ مفتاح API يبدو صحيحاً (32 حرف)")
            return True
        else:
            print(f"⚠️  طول مفتاح API غير عادي: {len(api_key)} حرف")
            return False
    else:
        print("❌ لم يتم العثور على مفتاح API")
        return False

def test_api_connection(api_key):
    """اختبار الاتصال بـ API"""
    print("\n🌐 اختبار الاتصال بـ OpenWeatherMap API...")
    print("-" * 40)
    
    # اختبار بسيط للطقس في الرياض
    test_url = "https://api.openweathermap.org/data/2.5/weather"
    test_params = {
        'lat': 24.7136,
        'lon': 46.6753,
        'appid': api_key,
        'units': 'metric',
        'lang': 'ar'
    }
    
    try:
        print("🔄 جاري اختبار API...")
        response = requests.get(test_url, params=test_params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            city_name = data.get('name', 'غير معروف')
            temp = data.get('main', {}).get('temp', 'غير متوفر')
            print(f"✅ API يعمل بنجاح!")
            print(f"📍 المدينة: {city_name}")
            print(f"🌡️  درجة الحرارة: {temp}°C")
            return True
            
        elif response.status_code == 401:
            print("❌ مفتاح API غير صحيح أو منتهي الصلاحية")
            print("💡 تأكد من:")
            print("   - صحة مفتاح API")
            print("   - أن المفتاح مفعل في حسابك")
            print("   - أن لديك رصيد كافي")
            return False
            
        elif response.status_code == 429:
            print("⚠️  تم تجاوز حد الطلبات المسموح")
            print("💡 انتظر قليلاً ثم حاول مرة أخرى")
            return False
            
        else:
            print(f"❌ خطأ في API: {response.status_code}")
            print(f"📝 الرسالة: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ انتهت مهلة الاتصال")
        print("💡 تحقق من اتصال الإنترنت")
        return False
        
    except requests.exceptions.ConnectionError:
        print("🌐 خطأ في الاتصال بالإنترنت")
        print("💡 تحقق من اتصال الإنترنت")
        return False
        
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        return False

def check_dependencies():
    """فحص المكتبات المطلوبة"""
    print("\n📦 فحص المكتبات المطلوبة...")
    print("-" * 40)
    
    required_packages = {
        'flask': 'Flask',
        'requests': 'requests',
        'python-dotenv': 'dotenv'
    }
    
    missing_packages = []
    
    for package, import_name in required_packages.items():
        try:
            if import_name == 'dotenv':
                import dotenv
            elif import_name == 'Flask':
                import flask
            elif import_name == 'requests':
                import requests
            print(f"✅ {package} مثبت")
        except ImportError:
            print(f"❌ {package} غير مثبت")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n💡 لتثبيت المكتبات المفقودة:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def test_local_server():
    """اختبار الخادم المحلي"""
    print("\n🖥️  اختبار الخادم المحلي...")
    print("-" * 40)
    
    try:
        response = requests.get('http://localhost:5000', timeout=5)
        if response.status_code == 200:
            print("✅ الخادم المحلي يعمل بنجاح")
            print("🌐 يمكنك الوصول للتطبيق على: http://localhost:5000")
            return True
        else:
            print(f"⚠️  الخادم يعمل لكن هناك مشكلة: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ الخادم المحلي لا يعمل")
        print("💡 تأكد من تشغيل التطبيق بالأمر: python app.py")
        return False
    except Exception as e:
        print(f"❌ خطأ في اختبار الخادم: {e}")
        return False

def provide_solutions():
    """تقديم حلول للمشاكل الشائعة"""
    print("\n🔧 حلول للمشاكل الشائعة:")
    print("=" * 50)
    
    print("\n1️⃣  مشكلة مفتاح API:")
    print("   • احصل على مفتاح مجاني من: https://openweathermap.org/api")
    print("   • أنشئ حساب جديد إذا لم يكن لديك")
    print("   • انسخ المفتاح وضعه في ملف .env")
    print("   • تأكد من عدم وجود مسافات إضافية")
    
    print("\n2️⃣  مشكلة الاتصال:")
    print("   • تحقق من اتصال الإنترنت")
    print("   • جرب VPN إذا كان الموقع محجوب")
    print("   • تأكد من عدم وجود firewall يحجب الاتصال")
    
    print("\n3️⃣  مشكلة المكتبات:")
    print("   • شغل: pip install -r requirements.txt")
    print("   • أو: pip install flask requests python-dotenv")
    print("   • تأكد من استخدام Python 3.7 أو أحدث")
    
    print("\n4️⃣  مشكلة التشغيل:")
    print("   • شغل: python app.py")
    print("   • أو استخدم: start.bat على Windows")
    print("   • تأكد من أن المنفذ 5000 غير مستخدم")

def main():
    """الدالة الرئيسية للتشخيص"""
    print("🏥 أداة تشخيص تطبيق الطقس")
    print("=" * 50)
    
    # فحص المكتبات
    deps_ok = check_dependencies()
    if not deps_ok:
        print("\n❌ يجب تثبيت المكتبات المفقودة أولاً")
        provide_solutions()
        return False
    
    # فحص البيئة
    env_ok = check_environment()
    
    if env_ok:
        # اختبار API
        api_key = os.getenv('OPENWEATHER_API_KEY')
        api_ok = test_api_connection(api_key)
        
        if api_ok:
            print("\n🎉 جميع الاختبارات نجحت!")
            print("✅ التطبيق جاهز للاستخدام مع بيانات حقيقية")
        else:
            print("\n⚠️  API لا يعمل - سيتم استخدام بيانات تجريبية")
    else:
        print("\n⚠️  مفتاح API غير صحيح - سيتم استخدام بيانات تجريبية")
    
    # اختبار الخادم المحلي
    server_ok = test_local_server()
    
    if not server_ok:
        print("\n💡 لتشغيل التطبيق:")
        print("python app.py")
    
    # تقديم الحلول
    provide_solutions()
    
    print("\n" + "=" * 50)
    print("🏁 انتهى التشخيص")
    
    return env_ok and deps_ok

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  تم إيقاف التشخيص")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ في التشخيص: {e}")
        sys.exit(1)
