{% extends "base.html" %}

{% block title %}طقس {{ city }} - تطبيق الطقس{% endblock %}

{% block content %}
<!-- Breadcrumb -->
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
        <li class="breadcrumb-item active" aria-current="page">{{ city }}</li>
    </ol>
</nav>

{% if current_weather %}
<!-- Current Weather Section -->
<div class="row mb-5">
    <div class="col-12">
        <div class="card current-weather-card shadow-lg border-0">
            <div class="card-body p-4">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h1 class="display-6 fw-bold mb-2">
                            <i class="bi bi-geo-alt-fill text-primary me-2"></i>
                            {{ city }}
                        </h1>
                        <p class="text-muted mb-3">
                            <i class="bi bi-clock me-1"></i>
                            آخر تحديث: الآن
                        </p>
                        
                        <div class="current-temp-section">
                            <div class="d-flex align-items-center mb-3">
                                <div class="weather-icon me-3">
                                    <img src="https://openweathermap.org/img/wn/{{ current_weather.weather[0].icon }}@4x.png" 
                                         alt="{{ current_weather.weather[0].description }}" 
                                         class="img-fluid" style="width: 100px;">
                                </div>
                                <div>
                                    <div class="temperature display-3 fw-bold text-primary">
                                        {{ current_weather.main.temp|round }}°C
                                    </div>
                                    <div class="feels-like text-muted">
                                        يبدو وكأنه {{ current_weather.main.feels_like|round }}°C
                                    </div>
                                </div>
                            </div>
                            
                            <div class="weather-description">
                                <h4 class="h5 text-capitalize">{{ current_weather.weather[0].description }}</h4>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="weather-details">
                            <div class="row g-3">
                                <div class="col-6">
                                    <div class="detail-card p-3 bg-light rounded">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-droplet-fill text-info me-2 fs-4"></i>
                                            <div>
                                                <div class="fw-bold">{{ current_weather.main.humidity }}%</div>
                                                <small class="text-muted">الرطوبة</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-6">
                                    <div class="detail-card p-3 bg-light rounded">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-wind text-primary me-2 fs-4"></i>
                                            <div>
                                                <div class="fw-bold">{{ current_weather.wind.speed }} م/ث</div>
                                                <small class="text-muted">سرعة الرياح</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-6">
                                    <div class="detail-card p-3 bg-light rounded">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-speedometer text-warning me-2 fs-4"></i>
                                            <div>
                                                <div class="fw-bold">{{ current_weather.main.pressure }} هكتوباسكال</div>
                                                <small class="text-muted">الضغط الجوي</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-6">
                                    <div class="detail-card p-3 bg-light rounded">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-eye-fill text-success me-2 fs-4"></i>
                                            <div>
                                                <div class="fw-bold">{{ (current_weather.visibility / 1000)|round(1) }} كم</div>
                                                <small class="text-muted">مدى الرؤية</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Sun Times -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="sun-time-card p-3 bg-warning bg-opacity-10 rounded">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-sunrise-fill text-warning me-2 fs-4"></i>
                                <div>
                                    <div class="fw-bold">{{ (current_weather.sys.sunrise | timestamp_to_time) }}</div>
                                    <small class="text-muted">شروق الشمس</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="sun-time-card p-3 bg-danger bg-opacity-10 rounded">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-sunset-fill text-danger me-2 fs-4"></i>
                                <div>
                                    <div class="fw-bold">{{ (current_weather.sys.sunset | timestamp_to_time) }}</div>
                                    <small class="text-muted">غروب الشمس</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% if forecast %}
<!-- 7-Day Forecast Section -->
<div class="row">
    <div class="col-12">
        <h2 class="h3 mb-4">
            <i class="bi bi-calendar-week me-2 text-primary"></i>
            التنبؤات لمدة أسبوع
        </h2>
        
        <div class="forecast-container">
            <div class="row g-3">
                {% for day in forecast %}
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="card forecast-card h-100 shadow-sm">
                        <div class="card-body text-center p-3">
                            <h6 class="card-title fw-bold mb-2">{{ day.day_name }}</h6>
                            <p class="card-subtitle text-muted small mb-3">{{ day.date }}</p>
                            
                            <div class="weather-icon mb-2">
                                <img src="https://openweathermap.org/img/wn/{{ day.icon }}@2x.png" 
                                     alt="{{ day.description }}" 
                                     class="img-fluid" style="width: 60px;">
                            </div>
                            
                            <div class="temperature-range mb-2">
                                <span class="temp-max fw-bold fs-5 text-danger">{{ day.temp_max }}°</span>
                                <span class="temp-min text-muted ms-2">{{ day.temp_min }}°</span>
                            </div>
                            
                            <p class="description small text-muted mb-2">{{ day.description }}</p>
                            
                            <div class="additional-info">
                                <div class="d-flex justify-content-between small text-muted">
                                    <span>
                                        <i class="bi bi-droplet-fill"></i>
                                        {{ day.humidity }}%
                                    </span>
                                    <span>
                                        <i class="bi bi-wind"></i>
                                        {{ day.wind_speed }} م/ث
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endif %}

{% else %}
<!-- Error State -->
<div class="row">
    <div class="col-12">
        <div class="alert alert-warning text-center" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            <h4 class="alert-heading">عذراً!</h4>
            <p>لا يمكن الحصول على بيانات الطقس لمدينة {{ city }} في الوقت الحالي.</p>
            <hr>
            <p class="mb-0">
                <a href="{{ url_for('index') }}" class="btn btn-primary">
                    <i class="bi bi-arrow-right me-1"></i>
                    العودة للرئيسية
                </a>
            </p>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_scripts %}
<script>
// تحديث الوقت كل دقيقة
setInterval(function() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-SA');
    // يمكن إضافة تحديث الوقت هنا إذا لزم الأمر
}, 60000);

// إضافة تأثيرات تفاعلية للبطاقات
document.querySelectorAll('.forecast-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-5px)';
        this.style.transition = 'transform 0.3s ease';
    });
    
    card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
    });
});
</script>
{% endblock %}
