#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبارات تطبيق الطقس
"""

import unittest
import json
from app import app, MIDDLE_EAST_CITIES

class WeatherAppTestCase(unittest.TestCase):
    """فئة اختبار تطبيق الطقس"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.app = app.test_client()
        self.app.testing = True
    
    def test_home_page(self):
        """اختبار الصفحة الرئيسية"""
        response = self.app.get('/')
        self.assertEqual(response.status_code, 200)
        self.assertIn('تطبيق الطقس', response.get_data(as_text=True))
        print("✅ اختبار الصفحة الرئيسية نجح")
    
    def test_weather_page_valid_city(self):
        """اختبار صفحة الطقس لمدينة صحيحة"""
        city = 'الرياض'
        response = self.app.get(f'/weather/{city}')
        self.assertEqual(response.status_code, 200)
        self.assertIn(city, response.get_data(as_text=True))
        print(f"✅ اختبار صفحة الطقس لمدينة {city} نجح")
    
    def test_weather_page_invalid_city(self):
        """اختبار صفحة الطقس لمدينة غير موجودة"""
        response = self.app.get('/weather/مدينة_غير_موجودة')
        self.assertEqual(response.status_code, 404)
        print("✅ اختبار المدينة غير الموجودة نجح")
    
    def test_api_endpoint_valid_city(self):
        """اختبار API endpoint لمدينة صحيحة"""
        city = 'دبي'
        response = self.app.get(f'/api/weather/{city}')
        self.assertEqual(response.status_code, 200)
        
        # التحقق من أن الاستجابة JSON صحيحة
        try:
            data = json.loads(response.get_data(as_text=True))
            self.assertIn('city', data)
            self.assertEqual(data['city'], city)
            print(f"✅ اختبار API لمدينة {city} نجح")
        except json.JSONDecodeError:
            print(f"⚠️  تحذير: استجابة API ليست JSON صحيح لمدينة {city}")
    
    def test_api_endpoint_invalid_city(self):
        """اختبار API endpoint لمدينة غير موجودة"""
        response = self.app.get('/api/weather/مدينة_غير_موجودة')
        self.assertEqual(response.status_code, 404)
        print("✅ اختبار API للمدينة غير الموجودة نجح")
    
    def test_cities_data_structure(self):
        """اختبار بنية بيانات المدن"""
        self.assertIsInstance(MIDDLE_EAST_CITIES, dict)
        self.assertGreater(len(MIDDLE_EAST_CITIES), 0)
        
        # التحقق من بنية كل مدينة
        for city_name, city_data in MIDDLE_EAST_CITIES.items():
            self.assertIn('lat', city_data)
            self.assertIn('lon', city_data)
            self.assertIn('country', city_data)
            self.assertIn('country_name', city_data)
            
            # التحقق من أن الإحداثيات أرقام
            self.assertIsInstance(city_data['lat'], (int, float))
            self.assertIsInstance(city_data['lon'], (int, float))
        
        print(f"✅ اختبار بنية بيانات {len(MIDDLE_EAST_CITIES)} مدينة نجح")
    
    def test_template_filter(self):
        """اختبار فلتر تحويل الوقت"""
        with app.app_context():
            from app import timestamp_to_time
            
            # اختبار timestamp صحيح
            test_timestamp = 1609459200  # 2021-01-01 00:00:00 UTC
            result = timestamp_to_time(test_timestamp)
            self.assertIsInstance(result, str)
            self.assertRegex(result, r'\d{2}:\d{2}')
            
            # اختبار timestamp غير صحيح
            invalid_result = timestamp_to_time("invalid")
            self.assertEqual(invalid_result, "غير متوفر")
        
        print("✅ اختبار فلتر تحويل الوقت نجح")

def run_comprehensive_test():
    """تشغيل اختبار شامل للتطبيق"""
    print("🧪 بدء الاختبارات الشاملة لتطبيق الطقس")
    print("=" * 60)
    
    # تشغيل اختبارات unittest
    suite = unittest.TestLoader().loadTestsFromTestCase(WeatherAppTestCase)
    runner = unittest.TextTestRunner(verbosity=0)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    
    if result.wasSuccessful():
        print("🎉 جميع الاختبارات نجحت!")
        print(f"✅ تم تشغيل {result.testsRun} اختبار بنجاح")
    else:
        print("❌ بعض الاختبارات فشلت:")
        print(f"🔴 فشل: {len(result.failures)} اختبار")
        print(f"⚠️  أخطاء: {len(result.errors)} اختبار")
        
        # طباعة تفاصيل الأخطاء
        for test, error in result.failures + result.errors:
            print(f"\n❌ {test}: {error}")
    
    print("\n" + "=" * 60)
    
    # اختبارات إضافية
    print("🔍 اختبارات إضافية:")
    
    # التحقق من الملفات المطلوبة
    import os
    required_files = [
        'app.py', 'weather_service.py', 'requirements.txt',
        'templates/base.html', 'templates/index.html', 'templates/weather.html',
        'static/css/style.css', 'static/js/app.js'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"⚠️  ملفات مفقودة: {', '.join(missing_files)}")
    else:
        print("✅ جميع الملفات المطلوبة موجودة")
    
    # التحقق من متغيرات البيئة
    from dotenv import load_dotenv
    load_dotenv()
    
    api_key = os.getenv('OPENWEATHER_API_KEY')
    if not api_key or api_key == 'your_api_key_here':
        print("⚠️  مفتاح API غير مُعرَّف أو غير صحيح")
    else:
        print("✅ مفتاح API موجود")
    
    print("\n🏁 انتهت الاختبارات")
    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_comprehensive_test()
    exit(0 if success else 1)
